// TraceSlickGrid Hook Module
// This module contains the large TraceSlickGrid hook extracted from app.js

import { SlickGrid, SlickDataView, SlickGroupItemMetadataProvider } from "slickgrid"

// Hook for SlickGrid-based trace table with client-side filtering and pagination
const TraceSlickGrid = {
  mounted() {
    console.debug('TraceSlickGrid hook mounted');
    this.initializeSlickGrid();
    this.setupClientSideFiltering();
    this.setupStreamObserver();

    // Listen for grid data update events
    this.handleEvent("grid_data_update", (data) => {
      console.debug('Received grid_data_update event:', data);
      this.updateGridDataFromEvent(data);
    });

    // Listen for export events
    this.handleEvent("export_data", (data) => {
      console.debug('Received export_data event:', data);
      this.handleExportEvent(data);
    });

    console.debug('TraceSlickGrid hook initialized');
  },

  updated() {
    console.debug('TraceSlickGrid hook updated');
    // Update filters when component data attributes change
    this.updateFiltersFromDataAttributes();

    // Since we use phx-update="ignore", we need to manually check for data changes
    // The data attributes won't be automatically updated by LiveView
    console.debug('TraceSlickGrid updated hook called');
  },

  destroyed() {
    console.debug('TraceSlickGrid hook destroyed - cleaning up grid instance');

    // Clean up stream observer
    if (this.streamObserver) {
      this.streamObserver.disconnect();
      this.streamObserver = null;
    }

    // Clean up resize listener
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }

    // Clean up SlickGrid instance
    if (this.grid) {
      try {
        // Unsubscribe from events to prevent memory leaks
        if (this.grid.onClick) {
          this.grid.onClick.unsubscribe();
        }

        // Destroy the grid instance
        this.grid.destroy();
        console.debug('SlickGrid instance destroyed');
      } catch (error) {
        console.warn('Error destroying SlickGrid instance:', error);
      }
      this.grid = null;
    }

    // Clean up DataView
    if (this.dataView) {
      try {
        // Unsubscribe from DataView events
        if (this.dataView.onRowCountChanged) {
          this.dataView.onRowCountChanged.unsubscribe();
        }
        if (this.dataView.onRowsChanged) {
          this.dataView.onRowsChanged.unsubscribe();
        }
        if (this.dataView.onGroupCollapsed) {
          this.dataView.onGroupCollapsed.unsubscribe();
        }
        if (this.dataView.onGroupExpanded) {
          this.dataView.onGroupExpanded.unsubscribe();
        }
      } catch (error) {
        console.warn('Error cleaning up DataView events:', error);
      }
      this.dataView = null;
    }

    // Clean up other references
    this.groupItemMetadataProvider = null;
    this.columns = null;
    this.options = null;
    this.filters = null;
    this.groupExpandStates = null;

    console.debug('TraceSlickGrid cleanup completed');
  },

  initializeSlickGrid() {
    const container = this.el;
    const brokerId = container.dataset.brokerName || 'default';

    console.debug('Initializing SlickGrid on container:', container);
    console.debug('Container ID:', container.id);
    console.debug('Broker ID:', brokerId);
    console.debug('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight);

    // Check if grid already exists to prevent double initialization
    if (this.grid) {
      console.warn('SlickGrid already initialized for broker:', brokerId, '- skipping initialization');
      return;
    }

    // Define columns for the trace grid with responsive width constraints
    this.columns = this.getResponsiveColumns();

    // Grid options - configured for read-only display like DaisyUI table
    this.options = {
      enableCellNavigation: true,
      enableColumnReorder: false,
      multiColumnSort: false,
      rowHeight: 28,
      headerRowHeight: 40,
      enableAsyncPostRender: false,
      forceFitColumns: this.shouldForceFitColumns(), // Dynamic based on screen size
      enableTextSelectionOnCells: false,
      viewportClass: 'trace-grid-viewport',
      editable: false,
      autoEdit: false,
      enableAddRow: false,
      enableCellRangeSelection: false,
      enableRowReordering: false,
      // Enable responsive behavior
      autoWidth: false, // Let columns use their defined widths
      syncColumnCellResize: true
    };

    // Initialize group item metadata provider for grouping functionality
    console.debug('Initializing SlickGroupItemMetadataProvider...');
    this.groupItemMetadataProvider = new SlickGroupItemMetadataProvider();
    console.debug('SlickGroupItemMetadataProvider initialized:', this.groupItemMetadataProvider);

    // Initialize data view for virtual scrolling and filtering
    this.dataView = new SlickDataView({
      groupItemMetadataProvider: this.groupItemMetadataProvider,
      inlineFilters: true
    });

    // Initialize filter state
    this.filters = {
      topic_filter: '',
      payload_filter: '',
      selected_client_ids: [],
      ignore_ping_packets: true,
      topic_grouping_enabled: false
    };

    // Initialize grouping state from data attributes
    const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
    this.groupingEnabled = topicGroupingEnabled;

    // Initialize group expand/collapse state storage
    this.groupExpandStates = new Map();

    // Initialize pending restoration flag
    this.pendingGroupStateRestoration = false;

    console.debug('Initial grouping state from data attributes:', topicGroupingEnabled);

    // Initialize the grid
    this.grid = new SlickGrid(container, this.dataView, this.columns, this.options);

    // Register the group item metadata provider to add expand/collapse group handlers
    this.grid.registerPlugin(this.groupItemMetadataProvider);

    // TODO: Re-enable failed ACK message styling after fixing group compatibility
    // For now, we'll disable this to ensure group functionality works correctly
    // this.dataView.getItemMetadata = this.getRowMetadata.bind(this);

    console.debug('SlickGrid initialized:', this.grid);
    console.debug('Grid viewport:', this.grid.getViewport());

    // Set up event handlers
    this.grid.onClick.subscribe((e, args) => {
      // Handle row clicks for message selection
      const item = this.dataView.getItem(args.row);
      if (item && !item.__group) {
        this.pushEventTo(this.el, 'select_message', { id: item.id });
      }
    });

    // Set up data view events
    this.dataView.onGroupCollapsed.subscribe((e, args) => {
      console.log('Group collapsed:', args.groupingKey);
      this.captureGroupStatesOnChange(args.groupingKey, false);        
      this.grid.render();

    });
    this.dataView.onGroupExpanded.subscribe((e, args) => {
      console.log('Group expanded:', args.groupingKey);
      this.captureGroupStatesOnChange(args.groupingKey, true);
      this.grid.render();

    });
    this.dataView.onRowsChanged.subscribe((e, args) => {
      // SlickGrid automatically handles rendering for onRowsChanged
      // Only invalidate specific rows if provided
      if (args.rows && args.rows.length > 0) {
        this.grid.invalidateRows(args.rows);
      } else {
        this.grid.invalidate();
      }

      this.updateMessageCount();

      // Handle pending group state restoration after grouping operations
      this.handlePendingGroupStateRestoration();
    });

    // Load initial data
    this.updateGridData();

    // Set up window resize listener for responsive behavior
    this.handleResize = this.debounce(() => {
      this.handleWindowResize();
    }, 250);

    window.addEventListener('resize', this.handleResize);

    // Force resize after initialization
    setTimeout(() => {
      this.grid.resizeCanvas();
      this.grid.render();
      console.debug('Grid resized and re-rendered');
      console.debug('Grid container HTML:', this.el.innerHTML.slice(0, 500));
      console.debug('Grid canvas element:', this.el.querySelector('.slick-viewport'));

      // Check computed styles
      const headerColumns = this.el.querySelector('.slick-header-columns');
      if (headerColumns) {
        const computedStyle = window.getComputedStyle(headerColumns);
        console.debug('Header columns computed style:', {
          left: computedStyle.left,
          width: computedStyle.width,
          position: computedStyle.position,
          display: computedStyle.display
        });
      }

      // Force autosizeColumns if available
      if (this.grid.autosizeColumns) {
        this.grid.autosizeColumns();
        console.debug('Autosized columns');
      }

      // Fix column positions
      this.fixColumnPositions();
    }, 100);
  },

  setupClientSideFiltering() {
    // Set up client-side filtering using SlickGrid's DataView
    try {
      console.debug('Setting up client-side filtering...');
      console.debug('DataView available:', !!this.dataView);
      console.debug('FilterFunction available:', !!this.filterFunction);

      // Initialize filters first before setting up filtering
      this.updateFiltersFromDataAttributes();

      // Now set up the filter function
      if (this.dataView) {
        // Create a filter function that has access to the hook's filters
        this.createAndSetFilterFunction();
        console.debug('Filter function set successfully');
      } else {
        console.warn('DataView not available for filtering setup');
      }
    } catch (error) {
      console.error('Error setting up client-side filtering:', error);
    }
  },

  // Create and set a filter function that works with SlickGrid's compilation
  createAndSetFilterFunction() {
    // Store a reference to the hook's filters in the DataView for access
    this.dataView._hookFilters = this.filters;
    this.dataView._hookInstance = this;

    // Create a filter function that can access the stored filters
    const filterFunction = function(item) {
      const filters = this._hookFilters;
      const hook = this._hookInstance;

      if (!filters) return true;

      // PING packet filter
      if (filters.ignore_ping_packets &&
          (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
        return false;
      }

      // Topic filter
      if (filters.topic_filter && filters.topic_filter.trim() !== '') {
        const topic = item.topic || '';
        if (!hook.matchesTopic(topic, filters.topic_filter)) {
          return false;
        }
      }

      // Payload filter
      if (filters.payload_filter && filters.payload_filter.trim() !== '') {
        const payload = item.payload || '';
        if (!payload.toLowerCase().includes(filters.payload_filter.toLowerCase())) {
          return false;
        }
      }

      // Client ID filter
      if (filters.selected_client_ids.length > 0) {
        if (!filters.selected_client_ids.includes(item.client_id)) {
          return false;
        }
      }

      return true;
    };

    this.dataView.setFilter(filterFunction);
  },

  // Update the filter function when filters change
  updateFilterFunction() {
    if (this.dataView) {
      this.dataView._hookFilters = this.filters;
      this.dataView.refresh();
    }
  },

  filterFunction(item) {
    // Apply all filters to determine if item should be visible

    // PING packet filter
    if (this.filters.ignore_ping_packets &&
        (item.type === 'PINGREQ' || item.type === 'PINGRESP')) {
      return false;
    }

    // Topic filter
    if (this.filters.topic_filter && this.filters.topic_filter.trim() !== '') {
      const topic = item.topic || '';
      if (!this.matchesTopic(topic, this.filters.topic_filter)) {
        return false;
      }
    }

    // Payload filter
    if (this.filters.payload_filter && this.filters.payload_filter.trim() !== '') {
      const payload = item.payload || '';
      if (!payload.toLowerCase().includes(this.filters.payload_filter.toLowerCase())) {
        return false;
      }
    }

    // Client ID filter
    if (this.filters.selected_client_ids.length > 0) {
      if (!this.filters.selected_client_ids.includes(item.client_id)) {
        return false;
      }
    }

    return true;
  },

  matchesTopic(topic, pattern) {
    // Simple MQTT topic matching - can be enhanced for wildcards
    if (pattern.includes('+') || pattern.includes('#')) {
      // Convert MQTT pattern to regex
      const regexPattern = pattern
        .replace(/\+/g, '[^/]+')
        .replace(/#/g, '.*');
      const regex = new RegExp(`^${regexPattern}$`);
      return regex.test(topic);
    } else {
      return topic.includes(pattern);
    }
  },

  updateFiltersFromDataAttributes() {
    // Read filter state from data attributes
    try {
      this.filters.topic_filter = this.el.dataset.topicFilter || '';
      this.filters.payload_filter = this.el.dataset.payloadFilter || '';
      this.filters.ignore_ping_packets = this.el.dataset.ignorePingPackets === 'true';
      this.filters.topic_grouping_enabled = this.el.dataset.topicGroupingEnabled === 'true';

      const selectedClientIds = this.el.dataset.selectedClientIds;
      if (selectedClientIds) {
        this.filters.selected_client_ids = JSON.parse(selectedClientIds);
      } else {
        this.filters.selected_client_ids = [];
      }

      // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
      const topicGroupingEnabled = this.el.dataset.topicGroupingEnabled === 'true';
      this.groupingEnabled = topicGroupingEnabled;
      this.filters.topic_grouping_enabled = topicGroupingEnabled;

      console.debug('Synced grouping state from data attributes:', topicGroupingEnabled);

      // Update grouping state - this will also ensure filters are properly applied
      this.updateGrouping(topicGroupingEnabled);

    } catch (error) {
      console.error('Error reading filter data attributes:', error);
    }
  },

  updateFiltersFromEvent(data) {
    console.debug('updateFiltersFromEvent called with data:', data);

    // Update filters from LiveView event
    this.filters.topic_filter = data.topic_filter || '';
    this.filters.payload_filter = data.payload_filter || '';
    this.filters.selected_client_ids = data.selected_client_ids || [];
    this.filters.ignore_ping_packets = data.ignore_ping_packets;
    this.filters.topic_grouping_enabled = data.topic_grouping_enabled || false;

    // Sync grouping state between this.groupingEnabled and this.filters.topic_grouping_enabled
    const topicGroupingEnabled = data.topic_grouping_enabled || false;
    this.groupingEnabled = topicGroupingEnabled;
    this.filters.topic_grouping_enabled = topicGroupingEnabled;

    console.debug('Synced grouping state from event:', topicGroupingEnabled);

    // Update grouping state - this will also ensure filters are properly applied
    this.updateGrouping(topicGroupingEnabled);
  },

  updateGridData() {
    try {
      const gridDataJson = this.el.dataset.gridData;
      if (gridDataJson) {
        const gridData = JSON.parse(gridDataJson);

        // Validate that gridData is an array
        if (!Array.isArray(gridData)) {
          console.warn('Grid data is not an array:', gridData);
          return;
        }

        // Ensure all items have valid IDs, generate if missing
        const validGridData = gridData.map(item => {
          if (!item || typeof item !== 'object') {
            console.warn('Filtering out invalid item:', item);
            return null;
          }

          // Generate ID if missing or invalid
          if (!item.hasOwnProperty('id') || item.id === null || item.id === undefined || item.id === '') {
            console.warn('Generating ID for item without valid id:', item);
            item.id = `generated_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          }

          // Ensure ID is a string for consistency
          item.id = String(item.id);

          return item;
        }).filter(item => item !== null);

        if (validGridData.length !== gridData.length) {
          console.warn(`Filtered out ${gridData.length - validGridData.length} invalid items`);
        }

        this.dataView.beginUpdate();
        // Use ID field for deduplication in all cases
        // SlickGrid requires unique IDs even when grouping is enabled
        this.dataView.setItems(validGridData, 'id');
        this.dataView.endUpdate();

        // Apply filters after loading data
        this.dataView.refresh();

        // Re-apply grouping if enabled, preserving existing group states
        if (this.groupingEnabled) {
          // Re-apply grouping configuration
          this.dataView.setGrouping([{
            getter: (item) => {
              // Extract topic for grouping - handle both direct topic field and topics array
              if (typeof item.topic === 'string' && item.topic) {
                return item.topic;
              } else if (item.topics && Array.isArray(item.topics) && item.topics.length > 0) {
                const firstTopic = item.topics[0];
                if (typeof firstTopic === 'object' && firstTopic.topic) {
                  return firstTopic.topic;
                } else if (typeof firstTopic === 'string') {
                  return firstTopic;
                }
              }
              return 'No Topic';
            },
            formatter: (g) => {
              // Format the group header with topic name and count
              const topicName = g.value || 'No Topic';
              return `<span class="font-semibold">Topic: ${topicName}</span> <span class="badge badge-sm badge-outline ml-2">${g.count} messages</span>`;
            },
            aggregateCollapsed: false, // Calculate aggregates for collapsed groups
            collapsed: true, // Start with groups collapsed by default
            lazyTotalsCalculation: true
          }]);

          // Group state restoration will be handled by the event-driven mechanism
        }

        // Force grid to render
        this.grid.invalidate();
        this.grid.render();

        // Fix column positions after render
        this.fixColumnPositions();

        // Message count will be updated by onRowsChanged event

        // Log for debugging
        console.debug(`Updated grid with ${gridData.length} items`);
        console.debug('Grid data length:', this.dataView.getLength());
        console.debug('Grid viewport after update:', this.grid.getViewport());
      }
    } catch (error) {
      console.error('Error updating grid data:', error);
      console.error('Grid data JSON:', this.el.dataset.gridData);
    }
  }
};

export { TraceSlickGrid };
